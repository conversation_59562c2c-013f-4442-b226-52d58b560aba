import { useRef, useEffect } from 'react';
import { useMap } from 'react-map-gl/maplibre';
import { useMapStore } from '../../../../stores/useMapStore';
import { TerraDraw } from 'terra-draw';
import { TerraDrawMapLibreGLAdapter } from 'terra-draw-maplibre-gl-adapter';
import { TerraDrawCircleMode } from 'terra-draw';
import * as turf from '@turf/turf';

export function useTerraDraw() {
  const { current: map } = useMap();
  const { setSpatialFilter, setDrawingMode } = useMapStore();
  const mapInstance = map?.getMap();
  const terraDrawRef = useRef<TerraDraw | null>(null);

  // 处理绘制完成
  const handleDrawComplete = () => {
    if (!terraDrawRef.current) return;

    // 获取绘制的要素
    const features = terraDrawRef.current.getSnapshot();

    if (features.length > 0) {
      const feature = features[features.length - 1]; // 获取最后一个要素

      if (feature.geometry.type === 'Polygon') {
        // 计算圆形的中心点和半径
        const center = turf.centroid(feature as any);
        const centerCoords = center.geometry.coordinates as [number, number];

        // 获取圆形的半径（使用第一个点到中心的距离）
        const firstPoint = feature.geometry.coordinates[0][0];
        const radius = turf.distance(centerCoords, firstPoint, { units: 'kilometers' });

        // 设置空间筛选
        setSpatialFilter({
          center: centerCoords,
          radius: radius,
        });

        // 清理绘制的要素
        try {
          terraDrawRef.current.clear();
        } catch (error) {
          // 忽略清理错误
        }
      }
    }

    // 停止绘制模式
    try {
      terraDrawRef.current.setMode('static');
    } catch (error) {
      // 忽略模式设置错误
    } finally {
      setDrawingMode(false); // 禁用绘制模式，恢复地图事件

      // 立即重置鼠标样式
      if (map) {
        const canvas = map.getCanvas();
        if (canvas) {
          canvas.style.cursor = '';
        }
      }
    }
  };

  // 检查地图是否完全准备好
  const isMapReady = (map: any) => {
    try {
      if (!map || !map.isStyleLoaded()) {
        return false;
      }

      // 检查地图容器是否准备好
      const canvas = map.getCanvas();
      if (!canvas || !canvas.parentElement) {
        return false;
      }

      // 检查地图是否有基本的图层结构
      const style = map.getStyle();
      if (!style || !style.layers) {
        return false;
      }

      return true;
    } catch (error) {
      console.warn('检查地图状态时出错:', error);
      return false;
    }
  };

  // 初始化 Terra Draw
  useEffect(() => {
    if (!mapInstance) {
      return;
    }

    let handleStyleLoad: (() => void) | null = null;
    let retryTimeout: NodeJS.Timeout | null = null;
    let initAttempts = 0;
    const maxAttempts = 3;

    const attemptInit = () => {
      // 添加延迟，确保地图完全准备好
      setTimeout(() => {
        if (isMapReady(mapInstance)) {
          initTerraDraw();
        } else if (initAttempts < maxAttempts) {
          initAttempts++;
          console.warn(`地图未完全准备好，第${initAttempts}次重试初始化Terra Draw`);
          retryTimeout = setTimeout(attemptInit, 500);
        } else {
          console.error('Terra Draw 初始化失败：地图未能在预期时间内准备好');
        }
      }, 200);
    };

    // 检查地图样式是否已经加载完成
    if (mapInstance.isStyleLoaded()) {
      attemptInit();
    } else {
      // 等待样式加载完成
      handleStyleLoad = () => {
        attemptInit();
        if (handleStyleLoad) {
          mapInstance.off('styledata', handleStyleLoad);
        }
      };
      mapInstance.on('styledata', handleStyleLoad);
    }

    function initTerraDraw() {
      try {
        // 最终检查地图状态
        if (!isMapReady(mapInstance)) {
          throw new Error('地图状态检查失败');
        }

        // 检测移动端环境
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        // 移动端坐标验证和修复
        if (isMobile) {
          // 为移动端添加额外的地图事件监听，确保坐标有效性
          const originalProject = mapInstance.project;
          const originalUnproject = mapInstance.unproject;

          // 包装project方法以验证坐标
          mapInstance.project = function (lngLat: any) {
            try {
              const result = originalProject.call(this, lngLat);
              // 验证结果是否有效
              if (result && typeof result.x === 'number' && typeof result.y === 'number' &&
                !isNaN(result.x) && !isNaN(result.y) && isFinite(result.x) && isFinite(result.y)) {
                return result;
              }
              console.warn('移动端坐标投影结果无效:', result);
              return { x: 0, y: 0 }; // 返回默认值
            } catch (error) {
              console.error('移动端坐标投影错误:', error);
              return { x: 0, y: 0 };
            }
          };

          // 包装unproject方法以验证坐标
          mapInstance.unproject = function (point: any) {
            try {
              // 验证输入坐标
              if (!point || typeof point.x !== 'number' || typeof point.y !== 'number' ||
                isNaN(point.x) || isNaN(point.y) || !isFinite(point.x) || !isFinite(point.y)) {
                console.warn('移动端输入坐标无效:', point);
                return { lng: 0, lat: 0 };
              }
              const result = originalUnproject.call(this, point);
              return result;
            } catch (error) {
              console.error('移动端坐标反投影错误:', error);
              return { lng: 0, lat: 0 };
            }
          };
        }

        const adapter = new TerraDrawMapLibreGLAdapter({
          map: mapInstance,
          coordinatePrecision: 9,
        });

        const draw = new TerraDraw({
          adapter,
          modes: [
            new TerraDrawCircleMode({
              styles: {
                fillColor: '#3b82f6',
                fillOpacity: 0.2,
                outlineColor: '#3b82f6',
                outlineWidth: 3,
              },
              // 移动端优化配置
              ...(isMobile && {
                // 移动端可能需要更大的触摸容差
                pointerDistance: 10, // 增加触摸容差
                // 其他移动端特定配置
              })
            }),
          ],
        });

        // 监听绘制完成事件
        draw.on('finish', (id) => {
          try {
            handleDrawComplete();
          } catch (error) {
            console.error('处理绘制完成事件时出错:', error);
          }
        });

        // 监听绘制开始事件
        draw.on('change', (ids, type) => {
          // 绘制变化时的处理
          if (isMobile) {
            // 移动端特殊处理
            console.log('移动端绘制变化:', { ids, type });
          }
        });

        // 监听绘制错误事件（如果支持）
        if (typeof draw.on === 'function') {
          try {
            draw.on('error', (error: any) => {
              console.error('Terra Draw 绘制错误:', error);
              // 移动端错误处理
              if (isMobile) {
                console.warn('移动端绘制错误，可能是触摸事件处理问题');
              }
            });
          } catch (e) {
            // 忽略不支持的事件监听器
          }
        }

        // 启动 Terra Draw
        draw.start();

        terraDrawRef.current = draw;
        console.log('✅ Terra Draw 初始化成功');
      } catch (error) {
        console.error('❌ Terra Draw 初始化失败:', error);
        terraDrawRef.current = null;

        // 如果是移动端，可能需要特殊处理
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        if (isMobile) {
          console.warn('检测到移动端环境，Terra Draw 可能需要用户交互后才能正常工作');
        }
      }
    }

    // 清理函数
    return () => {
      // 清理样式加载监听器
      if (mapInstance && handleStyleLoad) {
        mapInstance.off('styledata', handleStyleLoad);
      }

      // 清理重试定时器
      if (retryTimeout) {
        clearTimeout(retryTimeout);
      }

      if (terraDrawRef.current) {
        try {
          // 检查地图实例是否仍然有效
          if (mapInstance && mapInstance.getCanvas()) {
            terraDrawRef.current.stop();
          }
        } catch (error) {
          // 忽略清理错误，因为地图可能已经被销毁
          console.warn('Terra Draw 清理时出现警告:', error);
        } finally {
          terraDrawRef.current = null;
        }
      }
    };
  }, [mapInstance]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (terraDrawRef.current) {
        try {
          // 检查地图实例是否仍然有效
          if (mapInstance && mapInstance.getCanvas()) {
            terraDrawRef.current.stop();
          }
        } catch (error) {
          // 忽略清理错误，因为地图可能已经被销毁
        } finally {
          terraDrawRef.current = null;
        }
      }
    };
  }, [mapInstance]);

  return {
    terraDrawRef,
    handleDrawComplete,
  };
}
