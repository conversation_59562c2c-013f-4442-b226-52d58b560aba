import React, { useState, useEffect } from 'react';
import { useMap } from 'react-map-gl/maplibre';
import { useMapStore } from '../../../stores/useMapStore';
import * as turf from '@turf/turf';
import { useTerraDraw } from './hooks/useTerraDraw';
import { useLocationPicking } from './hooks/useLocationPicking';
import { useSearch } from './hooks/useSearch';
import { ToolbarButtons } from './ToolbarButtons';
import { ManualFilterPanel } from './ManualFilterPanel';
import { StatusDisplay } from './StatusDisplay';

/**
 * 空间筛选工具条
 * 支持圆形绘制和手动设置功能
 */
export function SpatialFilterToolbar() {
  const { current: map } = useMap();
  const {
    filters,
    setSpatialFilter,
    setDrawingMode,
  } = useMapStore();
  const mapInstance = map?.getMap();

  const [longitude, setLongitude] = useState('');
  const [latitude, setLatitude] = useState('');
  const [radius, setRadius] = useState('10');
  const [isDrawing, setIsDrawing] = useState(false);
  const [showManualFilter, setShowManualFilter] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);

  // 使用自定义 hooks
  const { terraDrawRef } = useTerraDraw();
  const {
    isPickingLocation,
    locationPickSuccess,
    successMessage,
    startLocationPicking,
    cleanupLocationPicking,
  } = useLocationPicking();
  const {
    searchQuery,
    searchResults,
    isSearching,
    selectedSearchResult,
    updateSearchQuery,
    selectSearchResult,
    clearSelectedResult,
  } = useSearch();

  // 监听空间筛选状态变化，更新手动设置面板的输入框
  useEffect(() => {
    if (filters.spatialFilter.center && filters.spatialFilter.radius) {
      const [lng, lat] = filters.spatialFilter.center;
      setLongitude(lng.toFixed(6));
      setLatitude(lat.toFixed(6));
      setRadius(filters.spatialFilter.radius.toFixed(1));
      // 当有活动筛选时，清空搜索结果（因为坐标已经从筛选中获取）
      if (selectedSearchResult) {
        clearSelectedResult();
      }
    } else {
      // 只有在没有选中搜索结果且输入框为空时才重置半径
      // 这样可以避免用户手动输入时被清空，同时保持搜索结果选中状态
      if (!selectedSearchResult && !longitude && !latitude) {
        setRadius('10');
      }
    }
  }, [filters.spatialFilter, clearSelectedResult, selectedSearchResult, longitude, latitude]);

  // 选择搜索结果
  const handleSelectSearchResult = (result: any) => {
    const coords = selectSearchResult(result);
    setLongitude(coords.longitude);
    setLatitude(coords.latitude);
  };

  // 手动修改经纬度时清除选中的搜索结果
  const handleLongitudeChange = (value: string) => {
    setLongitude(value);
    if (selectedSearchResult) {
      clearSelectedResult();
    }
  };

  const handleLatitudeChange = (value: string) => {
    setLatitude(value);
    if (selectedSearchResult) {
      clearSelectedResult();
    }
  };

  // 开始地图选点
  const handleStartLocationPicking = () => {
    const onLocationSelected = (lng: number, lat: number) => {
      setLongitude(lng.toFixed(6));
      setLatitude(lat.toFixed(6));
    };

    const stopDrawing = () => {
      if (terraDrawRef.current) {
        try {
          terraDrawRef.current.setMode('static');
          terraDrawRef.current.clear();
          setIsDrawing(false);
          setDrawingMode(false);
        } catch (error) {
          // 忽略清理错误
        }
      }
    };

    startLocationPicking(onLocationSelected, isDrawing, stopDrawing);
  };

  // 检查terra-draw是否可用
  const isTerraDrawReady = () => {
    try {
      return terraDrawRef.current &&
             terraDrawRef.current.getSnapshot !== undefined &&
             map &&
             map.isStyleLoaded();
    } catch (error) {
      return false;
    }
  };

  // 开始绘制
  const handleStartDrawing = () => {
    if (!map) {
      alert('地图未加载完成，请稍后再试');
      return;
    }

    if (!isTerraDrawReady()) {
      console.warn('Terra Draw 未准备好，尝试重新初始化...');
      alert('绘制工具未准备好，请稍后再试。如果问题持续，请刷新页面');
      return;
    }

    // 如果正在地图选点，先取消选点
    if (isPickingLocation) {
      cleanupLocationPicking();
    }

    if (isDrawing) {
      // 取消绘制
      try {
        if (terraDrawRef.current) {
          terraDrawRef.current.setMode('static');
          terraDrawRef.current.clear();
        }
      } catch (error) {
        console.warn('取消绘制时出现警告:', error);
      } finally {
        setIsDrawing(false);
        setDrawingMode(false); // 禁用绘制模式，恢复地图事件

        // 立即重置鼠标样式
        const canvas = map.getCanvas();
        if (canvas) {
          canvas.style.cursor = '';
          canvas.title = '';
        }
      }
      return;
    }

    try {
      // 立即设置鼠标样式（不等待状态更新）
      const canvas = map.getCanvas();
      if (canvas) {
        canvas.style.cursor = 'crosshair';
      }

      // 设置状态
      setIsDrawing(true);
      setDrawingMode(true); // 启用绘制模式，禁用地图事件

      // 启动绘制模式
      terraDrawRef.current!.setMode('circle');
      console.log('✅ 绘制模式已启动');
    } catch (error) {
      console.error('❌ 启动绘制模式失败:', error);

      // 检查是否是移动端
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      const errorMessage = isMobile
        ? '移动端绘制功能可能需要特殊处理，请尝试使用手动设置功能'
        : '启动绘制模式失败，请刷新页面重试';

      alert(errorMessage);
      setIsDrawing(false);
      setDrawingMode(false); // 禁用绘制模式，恢复地图事件

      // 立即重置鼠标样式
      const canvas = map.getCanvas();
      if (canvas) {
        canvas.style.cursor = '';
      }
    }
  };

  // 手动设置筛选
  const handleManualFilter = () => {
    const lng = parseFloat(longitude);
    const lat = parseFloat(latitude);
    const r = parseFloat(radius || '10'); // 默认10公里

    if (isNaN(lng) || isNaN(lat)) {
      alert('请输入有效的经纬度');
      return;
    }

    if (isNaN(r) || r <= 0) {
      alert('请输入有效的半径（大于0）');
      return;
    }

    setSpatialFilter({
      center: [lng, lat] as [number, number],
      radius: r,
    });

    setShowManualFilter(false);
  };

  // 清除筛选
  const handleClearFilter = () => {
    if (isDrawing && terraDrawRef.current) {
      try {
        terraDrawRef.current.setMode('static');
        terraDrawRef.current.clear();
        setIsDrawing(false);
        setDrawingMode(false); // 禁用绘制模式，恢复地图事件
        
        // 立即重置鼠标样式
        if (map) {
          const canvas = map.getCanvas();
          if (canvas) {
            canvas.style.cursor = '';
          }
        }
      } catch (error) {
        // 忽略清理错误，确保状态被正确重置
        setIsDrawing(false);
        setDrawingMode(false);
        
        if (map) {
          const canvas = map.getCanvas();
          if (canvas) {
            canvas.style.cursor = '';
          }
        }
      }
    }

    // 清理地图选点状态
    cleanupLocationPicking();

    setSpatialFilter({
      center: null,
      radius: null,
    });
  };

  // 缩放到筛选区域
  const handleZoomToFilterArea = () => {
    if (!mapInstance || !filters.spatialFilter.center || !filters.spatialFilter.radius) {
      return;
    }

    const [lng, lat] = filters.spatialFilter.center;
    const radiusInKm = filters.spatialFilter.radius;

    // 计算包含圆形区域的边界框
    const center = turf.point([lng, lat]);
    const circle = turf.circle(center, radiusInKm, { units: 'kilometers' });
    const bbox = turf.bbox(circle);

    // 缩放到边界框，添加一些边距
    mapInstance.fitBounds(
      [[bbox[0], bbox[1]], [bbox[2], bbox[3]]], 
      {
        padding: 50,
        duration: 1000,
      }
    );
  };

  const hasActiveFilter = filters.spatialFilter.center && filters.spatialFilter.radius;

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-40">
      <div className="bg-white bg-opacity-95 backdrop-blur-sm rounded-lg shadow-xl border border-slate-200 px-3 py-2">
        <div className="flex items-center space-x-2">
          <ToolbarButtons
            isInitializing={isInitializing}
            isDrawing={isDrawing}
            isPickingLocation={isPickingLocation}
            hasActiveFilter={!!hasActiveFilter}
            onStartDrawing={handleStartDrawing}
            onToggleManualFilter={() => setShowManualFilter(old => !old)}
            onZoomToFilterArea={handleZoomToFilterArea}
            onClearFilter={handleClearFilter}
            showManualFilter={showManualFilter}
          />

          <StatusDisplay
            isInitializing={isInitializing}
            isDrawing={isDrawing}
            isPickingLocation={isPickingLocation}
            hasActiveFilter={!!hasActiveFilter}
            selectedSearchResult={selectedSearchResult}
            filterRadius={filters.spatialFilter.radius}
          />
        </div>
      </div>

      {/* 手动筛选面板 - 移出工具栏容器以避免定位冲突 */}
      <ManualFilterPanel
        isOpen={showManualFilter}
        onClose={() => setShowManualFilter(false)}
        longitude={longitude}
        latitude={latitude}
        radius={radius}
        searchQuery={searchQuery}
        searchResults={searchResults}
        isSearching={isSearching}
        selectedSearchResult={selectedSearchResult}
        isPickingLocation={isPickingLocation}
        isDrawing={isDrawing}
        locationPickSuccess={locationPickSuccess}
        successMessage={successMessage}
        onLongitudeChange={handleLongitudeChange}
        onLatitudeChange={handleLatitudeChange}
        onRadiusChange={setRadius}
        onSearchQueryChange={updateSearchQuery}
        onSelectSearchResult={handleSelectSearchResult}
        onClearSelectedResult={clearSelectedResult}
        onStartLocationPicking={handleStartLocationPicking}
        onApplyFilter={handleManualFilter}
      />
    </div>
  );
}
