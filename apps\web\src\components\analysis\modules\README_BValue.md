# Dynamic B-Value Analysis Implementation

## 概述

本实现完全按照 Python 文件 `calc_bValue.py` 的算法重新编写了 TypeScript 版本的动态 b 值分析功能。

## 核心函数对照

### 1. 频率-震级分布 (FMD)
- **Python**: `fmd(mag, mbin)`
- **TypeScript**: `fmd(mag: number[], mbin: number): FMDResult`
- **功能**: 计算震级分布的频率统计

### 2. b值估算
- **Python**: `b_est(mag, mbin, mc)`
- **TypeScript**: `bEst(mag: number[], mbin: number, mc: number): BEstResult`
- **功能**: 使用最大似然法估算 b 值和不确定性

### 3. 最大曲率法 (MAXC)
- **Python**: `get_maxc(mag, mbin)`
- **TypeScript**: `getMaxc(mag: number[], mbin: number): number`
- **功能**: 找到频率-震级分布中的最大曲率点

### 4. MBS方法
- **Python**: `get_mbs(mag, mbin, dM=0.4, min_mc=-3)`
- **TypeScript**: `getMbs(mag: number[], mbin: number, dM = 0.4, minMc = -3): MBSResult`
- **功能**: 使用最大曲率+b值稳定性方法确定完整性震级

### 5. 动态b值计算
- **Python**: `calc_timeBV2(datetimes, mag)`
- **TypeScript**: `computeSlidingWindowBSeries(events, mc): BSeriesPoint[]`
- **功能**: 使用滑动窗口计算时间序列b值

### 6. b-positive方法
- **Python**: `b_positive(axB, sdatetimes, mag_list)`
- **TypeScript**: `computeBPositiveSeries(events, mc, diff = 0.02): BSeriesPoint[]`
- **功能**: 基于震级差的b值计算方法

### 7. 风险事件识别
- **Python**: `get_top_percent_range(numbers, percent)`
- **TypeScript**: `getTopPercentRange(numbers: number[], percent: number)`
- **功能**: 识别前N%最大震级的事件

## 关键参数对照

| 参数 | Python值 | TypeScript值 | 说明 |
|------|----------|--------------|------|
| mbin | 0.1 | 0.1 | 震级分组间隔 |
| DIFF | 0.02 | 0.02 | b-positive方法的震级差阈值 |
| dM | 0.4 | 0.4 | MBS方法的滚动平均窗口 |
| min_mc | -3 | -3 | 最小完整性震级 |

## 窗口大小逻辑

### 动态b值窗口 (calc_timeBV2)
- 事件数 < 200: 窗口大小 = 60
- 事件数 200-500: 窗口大小 = 80  
- 事件数 >= 500: 窗口大小 = 120

### b-positive窗口 (b_positive)
- 事件数 <= 200: Np = 45
- 事件数 200-300: Np = 60
- 事件数 300-400: Np = 70
- 事件数 400-500: Np = 80
- 事件数 500-600: Np = 90
- 事件数 600-700: Np = 95
- 事件数 > 700: Np = 95

## 数学公式

### b值计算
```
b = log10(e) / (mbar - (mc - mbin/2))
```

### a值计算
```
a = log10(n) + b * mc
```

### 不确定性计算
- **Aki不确定性**: `aki_unc = b / sqrt(n)`
- **Shibolt不确定性**: `shibolt_unc = 2.3 * b^2 * sqrt(variance / (n * (n-1)))`

### b-positive计算
```
b = 1 / (mean_mag_diff - DIFF) / ln(10)
```

## 主要改进

1. **完整性震级计算**: 从简单的 MAXC+0.2 方法改为标准的 MBS 方法
2. **窗口大小**: 完全按照 Python 版本的自适应窗口大小逻辑
3. **不确定性计算**: 使用 Shibolt 方法计算不确定性
4. **b-positive实现**: 完整实现包括震级差计算和bootstrap不确定性估算
5. **数值精度**: 确保所有数学计算与 Python 版本一致

## 验证

通过测试验证了核心函数的正确性，包括：
- FMD 计算
- b值估算
- MBS 方法
- 完整性震级确定

所有计算结果与预期一致，确保了与 Python 版本的算法兼容性。
