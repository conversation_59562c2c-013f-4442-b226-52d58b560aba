import { useState, useEffect, useCallback, useMemo } from 'react';
// helper interfaces and functions for the new dynamic b-value computation
interface BSeriesPoint {
  date: string;      // YYYY-MM-DD
  b: number;         // b value
  bInv: number;      // 1 / b
  unc: number;       // Shibolt uncertainty
}

/** Estimate completeness magnitude (Mc) using a simple MAXC + 0.2 method */
function estimateCompletenessMagnitude(magnitudes: number[]): number {
  if (magnitudes.length === 0) return 0.5;
  const sorted = [...magnitudes].sort((a, b) => a - b);
  const minMag = Math.floor(sorted[0] * 10) / 10;
  const maxMag = Math.ceil(sorted[sorted.length - 1] * 10) / 10;
  const bins: Record<number, number> = {};
  for (let m = minMag; m <= maxMag; m += 0.1) {
    const rounded = Math.round(m * 10) / 10;
    bins[rounded] = sorted.filter(v => v >= rounded && v < rounded + 0.1).length;
  }
  const maxBinCount = Math.max(...Object.values(bins));
  const modeMag = parseFloat(
    Object.entries(bins).find(([, count]) => count === maxBinCount)?.[0] ?? '0'
  );
  return modeMag + 0.2;
}

/** Calculate b value and Shibolt uncertainty for a set of magnitudes */
function calculateBAndUnc(
  magnitudes: number[],
  mc: number,
  mbin = 0.1,
): { b: number | null; unc: number } {
  const filtered = magnitudes.filter(m => m >= mc - mbin / 2);
  const n = filtered.length;
  if (n < 2) return { b: null, unc: 0 };
  const meanMag = filtered.reduce((s, m) => s + m, 0) / n;
  const b = Math.LOG10E / (meanMag - (mc - mbin / 2));
  const variance =
    filtered.reduce((s, m) => s + (m - meanMag) ** 2, 0) / (n * (n - 1));
  const unc = 2.3 * b * b * Math.sqrt(variance);
  return { b, unc };
}

/** Compute sliding-window dynamic b-value series (window size adaptive) */
function computeSlidingWindowBSeries(
  events: { date: string; magnitude: number }[],
  mc: number,
): BSeriesPoint[] {
  const total = events.length;
  let windowSize = 120;
  if (total < 200) windowSize = 60;
  else if (total < 500) windowSize = 80;

  const result: BSeriesPoint[] = [];
  for (let i = windowSize - 1; i < total; i++) {
    const win = events.slice(i - windowSize + 1, i + 1);
    const mags = win.map(e => e.magnitude);
    const { b, unc } = calculateBAndUnc(mags, mc);
    if (b && isFinite(b)) {
      result.push({ date: events[i].date, b, bInv: 1 / b, unc });
    }
  }
  return result;
}

/** Compute b-positive series based on magnitude differences (from b_positive function) */
function computeBPositiveSeries(
  events: { date: string; magnitude: number }[],
  mc: number,
  diff = 0.02,
): BSeriesPoint[] {
  if (events.length === 0) return [];
  
  // Calculate magnitude differences
  const magDiffs = [0]; // First event has no difference
  for (let i = 1; i < events.length; i++) {
    magDiffs.push(events[i].magnitude - events[i-1].magnitude);
  }
  
  // Filter events with magnitude difference >= DIFF and magnitude >= mc
  const filteredIndices: number[] = [];
  for (let i = 0; i < events.length; i++) {
    if (magDiffs[i] >= diff && events[i].magnitude >= mc) {
      filteredIndices.push(i);
    }
  }
  
  if (filteredIndices.length === 0) return [];
  
  // Determine window size Np based on event count
  const evtNum = filteredIndices.length;
  let Np = 45;
  if (evtNum > 200 && evtNum <= 300) Np = 60;
  else if (evtNum > 300 && evtNum <= 400) Np = 70;
  else if (evtNum > 400 && evtNum <= 500) Np = 80;
  else if (evtNum > 500 && evtNum <= 600) Np = 90;
  else if (evtNum > 600) Np = 95;
  
  const result: BSeriesPoint[] = [];
  
  // Calculate b-positive values
  for (let i = Np; i < filteredIndices.length; i++) {
    const windowIndices = filteredIndices.slice(i - Np + 1, i + 1);
    const windowMagDiffs = windowIndices.map(idx => magDiffs[idx]);
    
    const meanMagDiff = windowMagDiffs.reduce((s, m) => s + m, 0) / windowMagDiffs.length;
    const b = 1 / (meanMagDiff - diff) / Math.LN10;
    
    // Calculate uncertainty using bootstrap
    let bTmp = 0;
    const bootstrapSamples = 50;
    for (let j = 0; j < bootstrapSamples; j++) {
      const randomIndices = [];
      for (let k = 0; k < Math.floor(Np - 10); k++) {
        randomIndices.push(Math.floor(Math.random() * Np));
      }
      const randomMags = randomIndices.map(idx => windowMagDiffs[idx]);
      const randomMean = randomMags.reduce((s, m) => s + m, 0) / randomMags.length;
      bTmp += 1 / (randomMean - diff) / Math.LN10;
    }
    const unc = Math.abs(bTmp / bootstrapSamples - b);
    
    if (isFinite(b) && b > 0) {
      result.push({
        date: events[filteredIndices[i]].date,
        b,
        bInv: 1 / b,
        unc
      });
    }
  }
  
  return result;
}

/** Get risk events (top percentage of magnitudes) */
function getRiskEvents(
  events: { date: string; magnitude: number }[],
  topPercent = 2
): { date: string; magnitude: number }[] {
  if (events.length === 0) return [];
  
  const sortedMags = [...events].sort((a, b) => b.magnitude - a.magnitude);
  const topCount = Math.max(1, Math.ceil(events.length * topPercent / 100));
  const threshold = sortedMags[topCount - 1].magnitude;
  
  return events.filter(e => e.magnitude >= threshold);
}
import { useEarthquakeData } from '../../../hooks/useEarthquakeData';
import ReactECharts from 'echarts-for-react';



export function DynamicBValueAnalysis() {
  const { earthquakes } = useEarthquakeData();
  const [chartOption, setChartOption] = useState<any>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  // dynamic b-value series produced by the new algorithm
  const [bSeries, setBSeries] = useState<BSeriesPoint[]>([]);
  // b-positive series
  const [bPlusSeries, setBPlusSeries] = useState<BSeriesPoint[]>([]);
  // risk events (top 2% magnitude)
  const [riskEvents, setRiskEvents] = useState<{ date: string; magnitude: number }[]>([]);
  
    // 节流计算函数
  const throttledCalculate = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        calculateDynamicBValue();
      }, 100);
    };
  }, [earthquakes]);

  // 数据变化时自动计算
  useEffect(() => {
    if (earthquakes && earthquakes.length > 0) {
      throttledCalculate();
    } else {
      setChartOption(null);
        setBSeries([]);
        setBPlusSeries([]);
        setRiskEvents([]);
    }
  }, [earthquakes, throttledCalculate]);

    

  const calculateDynamicBValue = useCallback(() => {
    if (!earthquakes || earthquakes.length === 0) return;

    setIsCalculating(true);
    try {
      // Prepare sorted events (date string & magnitude)
      const events = earthquakes
        .filter(eq => eq.occurred_at && eq.magnitude != null && !isNaN(eq.magnitude))
        .map(eq => {
          const date = new Date(eq.occurred_at).toISOString().split('T')[0];
          return { date, magnitude: eq.magnitude as number };
        })
        .sort((a, b) => (a.date < b.date ? -1 : 1));

             if (events.length === 0) {
         setChartOption(null);
         setBSeries([]);
         setBPlusSeries([]);
         setRiskEvents([]);
         return;
       }

      // Completeness magnitude Mc
      const mc = estimateCompletenessMagnitude(events.map(e => e.magnitude));

      // Dynamic b-value series
      const seriesData = computeSlidingWindowBSeries(events, mc);
      setBSeries(seriesData);

      // b-positive series
      const bPlusData = computeBPositiveSeries(events, mc);
      setBPlusSeries(bPlusData);
        
      // Risk events (top 2% magnitude)
      const riskEventsData = getRiskEvents(events, 2);
      setRiskEvents(riskEventsData);

             // Seismicity scatter data (regular events in gray)
       const regularEvents = events.filter(e => !riskEventsData.some(r => r.date === e.date && r.magnitude === e.magnitude));
       const scatterData: [string, number][] = regularEvents.map(e => [e.date, e.magnitude]);
       
       // Risk events scatter data (top 2% magnitude in blue)
       const riskScatterData: [string, number][] = riskEventsData.map(e => [e.date, e.magnitude]);

       // Dynamic b-value uncertainty band preparation
       const datesForBand = seriesData.map(p => p.date);
       const lowerBand = seriesData.map(p => p.b - p.unc);
       const upperBand = seriesData.map(p => p.b + p.unc);
       const diffBand = upperBand.map((u, idx) => u - lowerBand[idx]);

       // B-plus uncertainty band preparation
       const datesForBPlusBand = bPlusData.map(p => p.date);
       const bPlusLowerBand = bPlusData.map(p => p.b - p.unc);
       const bPlusUpperBand = bPlusData.map(p => p.b + p.unc);
       const bPlusDiffBand = bPlusUpperBand.map((u, idx) => u - bPlusLowerBand[idx]);
      
       // X-axis categories
       const xAxisDates = Array.from(new Set(events.map(e => e.date)));
       xAxisDates.sort();

       const createAlignedArray = (arr: number[], dates: string[]) => {
         const m = new Map<string, number>();
         dates.forEach((d, i) => m.set(d, arr[i]));
         return xAxisDates.map(d => (m.has(d) ? m.get(d) : null));
       };

       // Dynamic b-value aligned arrays
       const alignedLower = createAlignedArray(lowerBand, datesForBand);
       const alignedDiff = createAlignedArray(diffBand, datesForBand);
       const alignedB = createAlignedArray(seriesData.map(p => p.b), datesForBand);
        
       // B-plus aligned arrays
       const alignedBPlusLower = createAlignedArray(bPlusLowerBand, datesForBPlusBand);
       const alignedBPlusDiff = createAlignedArray(bPlusDiffBand, datesForBPlusBand);
       const alignedBPlusB = createAlignedArray(bPlusData.map(p => p.b), datesForBPlusBand);

      const option = {
        backgroundColor: 'white',
         grid: { left: '4%', right: '2%', bottom: '8%', top: '20%', containLabel: true },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' },
          appendToBody: true,
          confine: false,
          backgroundColor: 'rgba(255,255,255,0.95)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: { color: '#000', fontSize: 11 }
        },
        legend: {
           data: ['地震事件', '风险事件', '动态b值', 'b-plus'], 
          top: 5,
          right: 20,
           textStyle: { fontSize: 11, color: '#000' }
        },
        xAxis: {
          type: 'category',
           data: xAxisDates,
          name: '日期',
          nameLocation: 'middle',
          nameGap: 35,
           nameTextStyle: { fontSize: 12, fontWeight: 'bold', color: '#000' },
           axisLine: { lineStyle: { color: '#000', width: 1.5 } },
          axisLabel: {
            fontSize: 10,
            color: '#000',
             formatter: (value: string) => {
               const d = new Date(value + 'T00:00:00');
               const m = (d.getMonth() + 1).toString().padStart(2, '0');
               const day = d.getDate().toString().padStart(2, '0');
               return `${m}-${day}`;
            },
          },
           axisTick: { lineStyle: { color: '#000', width: 1 }, length: 5 }
        },
        yAxis: [
          {
            type: 'value',
            name: '震级 (ML)',
             min: -0.5, 
            max: 4.0,
             nameTextStyle: { fontSize: 12, fontWeight: 'bold', color: '#000' },
             axisLine: { lineStyle: { color: '#000', width: 1.5 } },
             axisLabel: { fontSize: 10, color: '#000' },
             axisTick: { lineStyle: { color: '#000', width: 1 }, length: 5 },
             splitLine: { show: true, lineStyle: { color: '#f0f0f0', width: 1 } }
          },
          {
            type: 'value',
            name: 'b值',
             min: 0, 
             max: 2.5,
             nameTextStyle: { fontSize: 12, fontWeight: 'bold', color: '#000' },
             axisLine: { lineStyle: { color: '#000', width: 1.5 } },
             axisLabel: { fontSize: 10, color: '#000', formatter: (value: number) => value.toFixed(1) },
             axisTick: { lineStyle: { color: '#000', width: 1 }, length: 5 }
           },
        ],
        series: [
           // Regular seismicity (gray dots)
          {
             name: '地震事件',
            type: 'scatter',
            yAxisIndex: 0,
             data: scatterData,
            symbolSize: (val: any) => Math.max(3, val[1] * 4),
             itemStyle: { color: '#d9d9d9', borderColor: '#bfbfbf', borderWidth: 0.5 },
             emphasis: { disabled: true },
             tooltip: { show: false },
            z: 3
          },
           // Risk events (blue dots - top 2% magnitude)
           {
             name: '风险事件',
             type: 'scatter',
             yAxisIndex: 0,
             data: riskScatterData,
             symbolSize: (val: any) => Math.max(8, val[1] * 5),
             itemStyle: { color: '#1E90FF', borderColor: '#0066CC', borderWidth: 1 },
             emphasis: { scale: true },
             tooltip: { show: false },
             z: 5
           },
           // Mc baseline
           {
             name: 'Mc线',
            type: 'line',
            yAxisIndex: 0,
             data: xAxisDates.map(() => mc),
             lineStyle: { color: '#000', type: 'dashed', width: 1.5 },
             symbol: 'none',
             silent: true,
             z: 2
           },
           // Dynamic b-value uncertainty band (red)
           {
            name: '动态b值下限',
            type: 'line',
            yAxisIndex: 1,
            stack: 'dynamic_uncert',
            data: alignedLower,
            lineStyle: { opacity: 0 },
            showSymbol: false,
            areaStyle: { color: 'transparent' },
            emphasis: { disabled: true },
            silent: true,
            z: 1,
            zlevel: 1,
          },
           {
             name: '动态b值区间',
             type: 'line',
             yAxisIndex: 1,
             stack: 'dynamic_uncert',
             data: alignedDiff,
             lineStyle: { opacity: 0 },
             showSymbol: false,
             areaStyle: { color: 'rgba(255,0,0,0.25)' },
             emphasis: { disabled: true },
             silent: true,
             z: 2
           },
           // B-plus uncertainty band (orange)
           {
             name: 'b-plus下限',
             type: 'line',
             yAxisIndex: 1,
             stack: 'bplus_uncert',
             data: alignedBPlusLower,
             lineStyle: { opacity: 0 },
             showSymbol: false,
             areaStyle: { color: 'transparent' },
             emphasis: { disabled: true },
             silent: true,
             z: 1,
             zlevel: 2,
           },
          {
             name: 'b-plus区间',
            type: 'line',
            yAxisIndex: 1,
             stack: 'bplus_uncert',
             data: alignedBPlusDiff,
             lineStyle: { opacity: 0 },
             showSymbol: false,
             areaStyle: { color: 'rgba(242, 140, 0, 0.4)' },
             emphasis: { disabled: true },
             silent: true,
             z: 4,
             zlevel: 3
           },
           // Dynamic b-value line (red)
           {
             name: '动态b值',
             type: 'line',
             yAxisIndex: 1,
             data: alignedB,
             lineStyle: { color: '#FF0000', width: 2 },
             symbolSize: 4,
            symbol: 'circle',
             itemStyle: { color: '#FF0000', borderColor: '#FFFFFF', borderWidth: 1 },
             smooth: false,
             connectNulls: false,
             z: 4
           },
           // B-plus line (orange)
           {
             name: 'b-plus',
             type: 'line',
             yAxisIndex: 1,
             data: alignedBPlusB,
             lineStyle: { color: '#F26100', width: 2 },
            symbolSize: 4,
             symbol: 'circle',
             itemStyle: { color: '#F26100', borderColor: '#FFFFFF', borderWidth: 1 },
            smooth: false,
             connectNulls: false,
             z: 4
           },
           // b=1 baseline
           {
             name: 'b=1',
             type: 'line',
             yAxisIndex: 1,
             data: xAxisDates.map(() => 1),
             lineStyle: { color: '#000', width: 1.5 },
             symbol: 'none',
             silent: true,
            z: 2
           },
         ],
      };

      setChartOption(option);
    } finally {
      setIsCalculating(false);
    }
  }, [earthquakes]);

  // 统计信息
  const avgBValue = bSeries.length > 0 ? bSeries.reduce((s, v) => s + v.b, 0) / bSeries.length : null;
  const totalEarthquakes = earthquakes ? earthquakes.length : 0;
  
  let analysisSpanDays = 0;
  if (earthquakes && earthquakes.length > 0) {
    const dates = earthquakes
      .filter(eq => eq.occurred_at)
      .map(eq => new Date(eq.occurred_at))
      .sort((a, b) => a.getTime() - b.getTime());
    const first = dates[0];
    const last = dates[dates.length - 1];
    analysisSpanDays = Math.ceil((last.getTime() - first.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  }

  return (
    <div className="space-y-3">
      <div className="bg-slate-50 p-3 rounded">
        <h4 className="font-semibold mb-2 text-sm">动态<i>b</i>值时间序列分析</h4>
        <p className="text-xs text-slate-600 mb-2">
          基于累积历史数据分析地震活动的<i>b</i>值随时间变化
        </p>
        <div className="flex items-center gap-2 text-xs text-orange-600 bg-orange-50 p-1 rounded border-l-3 border-orange-400">
          <span className="text-orange-500">💡</span>
          <p className="flex-1">
            选择的日期越长，<i>b</i>值计算越稳定可靠
          </p>
        </div>
        
        {isCalculating && (
          <div className="text-xs text-blue-600 text-center py-1">
            正在分析数据...
          </div>
        )}
        
        {!isCalculating && (!earthquakes || earthquakes.length === 0) && (
          <div className="text-xs text-slate-500 text-center py-1">
            等待地震数据...
          </div>
        )}

      </div>

      {chartOption && (
        <div className="space-y-2">
          <div className="grid grid-cols-4 gap-2">
            <div className="bg-blue-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">地震总数</div>
              <div className="text-lg font-semibold text-blue-600">{totalEarthquakes}</div>
            </div>
            
            <div className="bg-green-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">时间跨度</div>
              <div className="text-lg font-semibold text-green-600">{analysisSpanDays}天</div>
            </div>
            
            <div className="bg-red-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">动态<i>b</i>值</div>
              <div className="text-lg font-semibold text-red-600">
                {avgBValue ? avgBValue.toFixed(3) : 'N/A'}
              </div>
            </div>

            <div className="bg-orange-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">风险事件</div>
              <div className="text-lg font-semibold text-orange-600">{riskEvents.length}</div>
            </div>
          </div>

          <div className="bg-white p-3 rounded border border-slate-200">
            <ReactECharts 
              option={chartOption} 
              style={{ height: '350px', width: '100%', overflow: 'visible' }}
              opts={{ renderer: 'canvas' }}
            />
          </div>
        </div>
      )}
    </div>
  );
} 