import { useState, useEffect, useCallback, useMemo } from 'react';

// Helper interfaces and functions for dynamic b-value computation (following Python calc_bValue.py)
interface BSeriesPoint {
  date: string;      // YYYY-MM-DD
  b: number;         // b value
  bInv: number;      // 1 / b
  unc: number;       // Shibolt uncertainty
}

interface FMDResult {
  mi: number[];        // magnitude bins
  nbmag: number[];     // number of events in each bin
  cumnbmag: number[];  // cumulative number of events
}

interface BEstResult {
  a: number;           // a-value
  b: number;           // b-value
  akiUnc: number;      // Aki uncertainty
  shiboltUnc: number;  // Shibolt uncertainty
}

interface MBSResult {
  mc: number;          // completeness magnitude
  mi: number[];        // magnitude bins
  b: number[];         // b-values for each cutoff
  bAvg: number[];      // average b-values
  shiboltUnc: number[]; // uncertainties
}

/**
 * Frequency-Magnitude Distribution (FMD) - equivalent to Python fmd() function
 */
function fmd(mag: number[], mbin: number): FMDResult {
  if (mag.length === 0) {
    return { mi: [], nbmag: [], cumnbmag: [] };
  }

  const minmag = Math.floor(Math.min(...mag) / mbin) * mbin; // Lowest magnitude bin
  const maxmag = Math.ceil(Math.max(...mag) / mbin) * mbin;  // Highest magnitude bin

  // Sequence of magnitude bins
  const mi: number[] = [];
  for (let m = minmag; m <= maxmag + mbin; m += mbin) {
    mi.push(Math.round(m * 10) / 10); // Round to avoid floating point errors
  }

  const nbm = mi.length; // No. of magnitude bins
  const cumnbmag: number[] = new Array(nbm).fill(0); // Cumulative no. of events

  // Get cumulative no. of events in mag bin and higher
  for (let i = 0; i < nbm; i++) {
    cumnbmag[i] = mag.filter(m => m > mi[i] - mbin / 2).length;
  }

  // Get no. of events in each mag bin
  const nbmag: number[] = [];
  for (let i = 0; i < nbm; i++) {
    const next = i < nbm - 1 ? cumnbmag[i + 1] : 0;
    nbmag.push(Math.abs(cumnbmag[i] - next));
  }

  return { mi, nbmag, cumnbmag };
}

/**
 * B-value estimation - equivalent to Python b_est() function
 */
function bEst(mag: number[], mbin: number, mc: number): BEstResult {
  const magAboveMc = mag.filter(m => m > Math.round(mc * 10) / 10 - mbin / 2);
  const n = magAboveMc.length;

  if (n < 2) {
    return { a: NaN, b: NaN, akiUnc: NaN, shiboltUnc: NaN };
  }

  const mbar = magAboveMc.reduce((sum, m) => sum + m, 0) / n; // Mean magnitude
  const b = Math.LOG10E / (mbar - (mc - mbin / 2)); // b-value from Eq 3
  const a = Math.log10(n) + b * mc; // 'a-value' for Eq 2
  const akiUnc = b / Math.sqrt(n); // Uncertainty estimate from Eq 4

  // Shibolt uncertainty from Eq 5
  const variance = magAboveMc.reduce((sum, m) => sum + (m - mbar) ** 2, 0) / (n * (n - 1));
  const shiboltUnc = 2.3 * b * b * Math.sqrt(variance);

  return { a, b, akiUnc, shiboltUnc };
}

/**
 * Get maximum curvature magnitude - equivalent to Python get_maxc() function
 */
function getMaxc(mag: number[], mbin: number): number {
  const thisFmd = fmd(mag, mbin);
  if (thisFmd.nbmag.length === 0) return 0.5;

  const maxIndex = thisFmd.nbmag.indexOf(Math.max(...thisFmd.nbmag));
  return Math.round(thisFmd.mi[maxIndex] * 10) / 10;
}

/**
 * MBS (Maximum curvature + b-value stability) method - equivalent to Python get_mbs() function
 */
function getMbs(mag: number[], mbin: number, dM = 0.4, minMc = -3): MBSResult {
  const thisFmd = fmd(mag, mbin);
  const thisMaxc = getMaxc(mag, mbin);

  if (thisFmd.mi.length === 0) {
    return { mc: thisMaxc, mi: [], b: [], bAvg: [], shiboltUnc: [] };
  }

  // Pre-allocate arrays
  const a: number[] = new Array(thisFmd.mi.length).fill(NaN);
  const b: number[] = new Array(thisFmd.mi.length).fill(NaN);
  const bAvg: number[] = new Array(thisFmd.mi.length).fill(NaN);
  const shiboltUnc: number[] = new Array(thisFmd.mi.length).fill(NaN);

  // Loop through each magnitude bin, using it as cut-off magnitude
  for (let i = 0; i < thisFmd.mi.length; i++) {
    const mi = Math.round(thisFmd.mi[i] * 10) / 10; // Cut-off magnitude
    if (thisFmd.cumnbmag[i] > 1) {
      const result = bEst(mag, mbin, mi);
      a[i] = result.a;
      b[i] = result.b;
      shiboltUnc[i] = result.shiboltUnc;
    }
  }

  // Loop through again, calculating rolling average b-value over following dM magnitude units
  const noBins = Math.round(dM / mbin); // 4 bins for dM=0.4, mbin=0.1
  const checkBvalStability: boolean[] = [];

  for (let i = 0; i < thisFmd.mi.length; i++) {
    if (i >= thisFmd.mi.length - (noBins + 1)) {
      bAvg[i] = NaN;
      continue;
    }

    const bSlice = b.slice(i, i + noBins + 1);
    if (bSlice.some(val => isNaN(val))) {
      bAvg[i] = NaN;
      checkBvalStability.push(false);
    } else {
      bAvg[i] = bSlice.reduce((sum, val) => sum + val, 0) / bSlice.length;
      checkBvalStability.push(Math.abs(bAvg[i] - b[i]) <= shiboltUnc[i]);
    }
  }

  let mc = thisMaxc; // Default to MAXC

  if (checkBvalStability.some(stable => stable)) {
    const bvalStablePoints: number[] = [];
    for (let i = 0; i < checkBvalStability.length; i++) {
      if (checkBvalStability[i]) {
        bvalStablePoints.push(thisFmd.mi[i]);
      }
    }

    const validStablePoints = bvalStablePoints.filter(point => point > minMc);
    if (validStablePoints.length > 0) {
      mc = Math.round(Math.min(...validStablePoints) * 10) / 10;
    }
  }

  return { mc, mi: thisFmd.mi, b, bAvg, shiboltUnc };
}

/**
 * Get top percent range - equivalent to Python get_top_percent_range() function
 */
function getTopPercentRange(numbers: number[], percent: number): { min: number; max: number } | null {
  if (!Array.isArray(numbers) || numbers.length === 0) {
    return null;
  }
  if (percent <= 0 || percent >= 100) {
    return null;
  }

  const sortedNums = [...numbers].sort((a, b) => b - a); // Sort descending
  const n = sortedNums.length;
  const k = Math.max(1, Math.ceil(n * percent / 100)); // Round up, ensure at least 1 element

  const topValues = sortedNums.slice(0, k);
  return {
    min: Math.min(...topValues),
    max: Math.max(...topValues)
  };
}

/**
 * Compute sliding-window dynamic b-value series - equivalent to Python calc_timeBV2() function
 */
function computeSlidingWindowBSeries(
  events: { date: string; magnitude: number }[],
  mc: number,
): BSeriesPoint[] {
  const dt: string[] = [];
  const bv: number[] = [];
  const shiboltUnc: number[] = [];
  const tmpMag: number[] = [];

  // Determine window size based on event count (same logic as Python)
  const totalEvents = events.length;
  let winNum = 120; // Default for >= 500 events
  if (totalEvents < 200) {
    winNum = 60;
  } else if (totalEvents >= 200 && totalEvents < 500) {
    winNum = 80;
  }

  console.log(`evt num: ${totalEvents}, dynamic window number: ${winNum}`);

  for (let i = 0; i < events.length; i++) {
    tmpMag.push(events[i].magnitude);

    // Dynamic b-value calculation
    if (i + 1 >= winNum) {
      const winMagList = tmpMag.slice(-winNum); // Last winNum magnitudes
      const result = bEst(winMagList, 0.1, mc);

      if (!isNaN(result.b) && isFinite(result.b)) {
        dt.push(events[i].date);
        bv.push(result.b);
        shiboltUnc.push(result.shiboltUnc);
      }
    }
  }

  // Convert to BSeriesPoint format
  const seriesPoints: BSeriesPoint[] = [];
  for (let i = 0; i < dt.length; i++) {
    if (bv[i] > 0 && isFinite(bv[i])) {
      seriesPoints.push({
        date: dt[i],
        b: bv[i],
        bInv: 1 / bv[i],
        unc: shiboltUnc[i]
      });
    }
  }

  return seriesPoints;
}

/**
 * Compute b-positive series - equivalent to Python b_positive() function
 */
function computeBPositiveSeries(
  events: { date: string; magnitude: number }[],
  mc: number,
  diff = 0.02,
): BSeriesPoint[] {
  if (events.length === 0) return [];

  const mag = events.map(e => e.magnitude);
  const startDt = new Date(events[0].date);

  // Convert dates to hours from start (equivalent to Python hour_list)
  const hourList = events.map(e => {
    const dt = new Date(e.date);
    return (dt.getTime() - startDt.getTime()) / (1000 * 60 * 60); // Convert to hours
  });

  // Calculate magnitude differences (equivalent to Python magdif)
  const magdif = [0]; // First event has no difference
  for (let i = 1; i < mag.length; i++) {
    magdif.push(mag[i] - mag[i - 1]);
  }

  // Filter events with magnitude difference >= DIFF
  const magdif1: number[] = [];
  const cata1: number[] = [];
  const mag1: number[] = [];

  for (let i = 0; i < magdif.length; i++) {
    if (magdif[i] >= diff) {
      magdif1.push(magdif[i]);
      cata1.push(hourList[i]);
      mag1.push(mag[i]);
    }
  }

  const evtNum = cata1.length;
  console.log(`b-positive input events number: ${mag.length}, events diff: ${evtNum}`);

  if (evtNum === 0) return [];

  // Determine window size Np based on event count (same logic as Python)
  let Np = 45;
  if (evtNum > 200 && evtNum <= 300) Np = 60;
  else if (evtNum > 300 && evtNum <= 400) Np = 70;
  else if (evtNum > 400 && evtNum <= 500) Np = 80;
  else if (evtNum > 500 && evtNum <= 600) Np = 90;
  else if (evtNum > 600 && evtNum <= 700) Np = 95;
  else if (evtNum > 700) Np = 95;

  console.log(`Np: ${Np}`);

  const cata1Dt: string[] = [];
  const bEvent: Array<[number, number]> = []; // [b_value, uncertainty]
  let counter = 0;

  for (let i = 0; i < cata1.length; i++) {
    if (mag1[i] < mc) {
      continue;
    }

    if (i < Np) {
      continue;
    }

    // Get magnitude differences for the window
    const magWindow = magdif1.slice(i - Np + 1, i + 1);
    const meanMag = magWindow.reduce((sum, m) => sum + m, 0) / magWindow.length;
    const bValue = 1 / (meanMag - diff) / Math.LN10;

    // Calculate uncertainty using bootstrap (same as Python)
    const bTmp: number[] = [];
    for (let j = 0; j < Np; j++) {
      // Random permutation and selection
      const shuffled = [...Array(Np).keys()].sort(() => Math.random() - 0.5);
      const selected = shuffled.slice(0, Math.floor(Np - 10));
      const randMag = selected.map(idx => magWindow[idx]);
      const randMean = randMag.reduce((sum, m) => sum + m, 0) / randMag.length;
      bTmp.push(1 / (randMean - diff) / Math.LN10);
    }

    const bTmpMean = bTmp.reduce((sum, b) => sum + b, 0) / bTmp.length;
    const uncertainty = Math.sqrt(bTmp.reduce((sum, b) => sum + (b - bTmpMean) ** 2, 0) / bTmp.length);

    // Convert hour back to date
    const eventDate = new Date(startDt.getTime() + cata1[i] * 60 * 60 * 1000);
    cata1Dt.push(eventDate.toISOString().split('T')[0]);

    bEvent.push([bValue, uncertainty]);
    counter++;
  }

  // Convert to BSeriesPoint format
  const result: BSeriesPoint[] = [];
  for (let i = 0; i < cata1Dt.length; i++) {
    const b = bEvent[i][0];
    const unc = bEvent[i][1];

    if (isFinite(b) && b > 0) {
      result.push({
        date: cata1Dt[i],
        b,
        bInv: 1 / b,
        unc
      });
    }
  }

  return result;
}

/**
 * Get risk events (top percentage of magnitudes) - equivalent to Python get_top_percent_range() usage
 */
function getRiskEvents(
  events: { date: string; magnitude: number }[],
  topPercent = 2
): { date: string; magnitude: number }[] {
  if (events.length === 0) return [];

  const magnitudes = events.map(e => e.magnitude);
  const range = getTopPercentRange(magnitudes, topPercent);

  if (!range) return [];

  // Filter events with magnitude >= threshold (minimum of top percent range)
  return events.filter(e => e.magnitude >= range.min);
}
import { useEarthquakeData } from '../../../hooks/useEarthquakeData';
import ReactECharts from 'echarts-for-react';



export function DynamicBValueAnalysis() {
  const { earthquakes } = useEarthquakeData();
  const [chartOption, setChartOption] = useState<any>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  // dynamic b-value series produced by the new algorithm
  const [bSeries, setBSeries] = useState<BSeriesPoint[]>([]);
  // b-positive series
  const [bPlusSeries, setBPlusSeries] = useState<BSeriesPoint[]>([]);
  // risk events (top 2% magnitude)
  const [riskEvents, setRiskEvents] = useState<{ date: string; magnitude: number }[]>([]);
  
    // 节流计算函数
  const throttledCalculate = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        calculateDynamicBValue();
      }, 100);
    };
  }, [earthquakes]);

  // 数据变化时自动计算
  useEffect(() => {
    if (earthquakes && earthquakes.length > 0) {
      throttledCalculate();
    } else {
      setChartOption(null);
        setBSeries([]);
        setBPlusSeries([]);
        setRiskEvents([]);
    }
  }, [earthquakes, throttledCalculate]);

    

  const calculateDynamicBValue = useCallback(() => {
    if (!earthquakes || earthquakes.length === 0) return;

    setIsCalculating(true);
    try {
      // Prepare sorted events (date string & magnitude)
      const events = earthquakes
        .filter(eq => eq.occurred_at && eq.magnitude != null && !isNaN(eq.magnitude))
        .map(eq => {
          const date = new Date(eq.occurred_at).toISOString().split('T')[0];
          return { date, magnitude: eq.magnitude as number };
        })
        .sort((a, b) => (a.date < b.date ? -1 : 1));

             if (events.length === 0) {
         setChartOption(null);
         setBSeries([]);
         setBPlusSeries([]);
         setRiskEvents([]);
         return;
       }

      // Completeness magnitude Mc using MBS method (same as Python)
      const magnitudes = events.map(e => e.magnitude);
      const mbsResult = getMbs(magnitudes, 0.1); // mbin = 0.1 as in Python
      const mc = mbsResult.mc;

      console.log(`Completeness magnitude (Mc) = ${mc}`);

      // Dynamic b-value series
      const seriesData = computeSlidingWindowBSeries(events, mc);
      setBSeries(seriesData);

      // b-positive series
      const bPlusData = computeBPositiveSeries(events, mc);
      setBPlusSeries(bPlusData);
        
      // Risk events (top 2% magnitude)
      const riskEventsData = getRiskEvents(events, 2);
      setRiskEvents(riskEventsData);

             // Seismicity scatter data (regular events in gray)
       const regularEvents = events.filter(e => !riskEventsData.some(r => r.date === e.date && r.magnitude === e.magnitude));
       const scatterData: [string, number][] = regularEvents.map(e => [e.date, e.magnitude]);
       
       // Risk events scatter data (top 2% magnitude in blue)
       const riskScatterData: [string, number][] = riskEventsData.map(e => [e.date, e.magnitude]);

       // Dynamic b-value uncertainty band preparation
       const datesForBand = seriesData.map(p => p.date);
       const lowerBand = seriesData.map(p => p.b - p.unc);
       const upperBand = seriesData.map(p => p.b + p.unc);
       const diffBand = upperBand.map((u, idx) => u - lowerBand[idx]);

       // B-plus uncertainty band preparation
       const datesForBPlusBand = bPlusData.map(p => p.date);
       const bPlusLowerBand = bPlusData.map(p => p.b - p.unc);
       const bPlusUpperBand = bPlusData.map(p => p.b + p.unc);
       const bPlusDiffBand = bPlusUpperBand.map((u, idx) => u - bPlusLowerBand[idx]);
      
       // X-axis categories
       const xAxisDates = Array.from(new Set(events.map(e => e.date)));
       xAxisDates.sort();

       const createAlignedArray = (arr: number[], dates: string[]) => {
         const m = new Map<string, number>();
         dates.forEach((d, i) => m.set(d, arr[i]));
         return xAxisDates.map(d => (m.has(d) ? m.get(d) : null));
       };

       // Dynamic b-value aligned arrays
       const alignedLower = createAlignedArray(lowerBand, datesForBand);
       const alignedDiff = createAlignedArray(diffBand, datesForBand);
       const alignedB = createAlignedArray(seriesData.map(p => p.b), datesForBand);
        
       // B-plus aligned arrays
       const alignedBPlusLower = createAlignedArray(bPlusLowerBand, datesForBPlusBand);
       const alignedBPlusDiff = createAlignedArray(bPlusDiffBand, datesForBPlusBand);
       const alignedBPlusB = createAlignedArray(bPlusData.map(p => p.b), datesForBPlusBand);

      const option = {
        backgroundColor: 'white',
         grid: { left: '4%', right: '2%', bottom: '8%', top: '20%', containLabel: true },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' },
          appendToBody: true,
          confine: false,
          backgroundColor: 'rgba(255,255,255,0.95)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: { color: '#000', fontSize: 11 }
        },
        legend: {
           data: ['地震事件', '风险事件', '动态b值', 'b-plus'], 
          top: 5,
          right: 20,
           textStyle: { fontSize: 11, color: '#000' }
        },
        xAxis: {
          type: 'category',
           data: xAxisDates,
          name: '日期',
          nameLocation: 'middle',
          nameGap: 35,
           nameTextStyle: { fontSize: 12, fontWeight: 'bold', color: '#000' },
           axisLine: { lineStyle: { color: '#000', width: 1.5 } },
          axisLabel: {
            fontSize: 10,
            color: '#000',
             formatter: (value: string) => {
               const d = new Date(value + 'T00:00:00');
               const m = (d.getMonth() + 1).toString().padStart(2, '0');
               const day = d.getDate().toString().padStart(2, '0');
               return `${m}-${day}`;
            },
          },
           axisTick: { lineStyle: { color: '#000', width: 1 }, length: 5 }
        },
        yAxis: [
          {
            type: 'value',
            name: '震级 (ML)',
             min: -0.5, 
            max: 4.0,
             nameTextStyle: { fontSize: 12, fontWeight: 'bold', color: '#000' },
             axisLine: { lineStyle: { color: '#000', width: 1.5 } },
             axisLabel: { fontSize: 10, color: '#000' },
             axisTick: { lineStyle: { color: '#000', width: 1 }, length: 5 },
             splitLine: { show: true, lineStyle: { color: '#f0f0f0', width: 1 } }
          },
          {
            type: 'value',
            name: 'b值',
             min: 0, 
             max: 2.5,
             nameTextStyle: { fontSize: 12, fontWeight: 'bold', color: '#000' },
             axisLine: { lineStyle: { color: '#000', width: 1.5 } },
             axisLabel: { fontSize: 10, color: '#000', formatter: (value: number) => value.toFixed(1) },
             axisTick: { lineStyle: { color: '#000', width: 1 }, length: 5 }
           },
        ],
        series: [
           // Regular seismicity (gray dots)
          {
             name: '地震事件',
            type: 'scatter',
            yAxisIndex: 0,
             data: scatterData,
            symbolSize: (val: any) => Math.max(3, val[1] * 4),
             itemStyle: { color: '#d9d9d9', borderColor: '#bfbfbf', borderWidth: 0.5 },
             emphasis: { disabled: true },
             tooltip: { show: false },
            z: 3
          },
           // Risk events (blue dots - top 2% magnitude)
           {
             name: '风险事件',
             type: 'scatter',
             yAxisIndex: 0,
             data: riskScatterData,
             symbolSize: (val: any) => Math.max(8, val[1] * 5),
             itemStyle: { color: '#1E90FF', borderColor: '#0066CC', borderWidth: 1 },
             emphasis: { scale: true },
             tooltip: { show: false },
             z: 5
           },
           // Mc baseline
           {
             name: 'Mc线',
            type: 'line',
            yAxisIndex: 0,
             data: xAxisDates.map(() => mc),
             lineStyle: { color: '#000', type: 'dashed', width: 1.5 },
             symbol: 'none',
             silent: true,
             z: 2
           },
           // Dynamic b-value uncertainty band (red)
           {
            name: '动态b值下限',
            type: 'line',
            yAxisIndex: 1,
            stack: 'dynamic_uncert',
            data: alignedLower,
            lineStyle: { opacity: 0 },
            showSymbol: false,
            areaStyle: { color: 'transparent' },
            emphasis: { disabled: true },
            silent: true,
            z: 1,
            zlevel: 1,
          },
           {
             name: '动态b值区间',
             type: 'line',
             yAxisIndex: 1,
             stack: 'dynamic_uncert',
             data: alignedDiff,
             lineStyle: { opacity: 0 },
             showSymbol: false,
             areaStyle: { color: 'rgba(255,0,0,0.25)' },
             emphasis: { disabled: true },
             silent: true,
             z: 2
           },
           // B-plus uncertainty band (orange)
           {
             name: 'b-plus下限',
             type: 'line',
             yAxisIndex: 1,
             stack: 'bplus_uncert',
             data: alignedBPlusLower,
             lineStyle: { opacity: 0 },
             showSymbol: false,
             areaStyle: { color: 'transparent' },
             emphasis: { disabled: true },
             silent: true,
             z: 1,
             zlevel: 2,
           },
          {
             name: 'b-plus区间',
            type: 'line',
            yAxisIndex: 1,
             stack: 'bplus_uncert',
             data: alignedBPlusDiff,
             lineStyle: { opacity: 0 },
             showSymbol: false,
             areaStyle: { color: 'rgba(242, 140, 0, 0.4)' },
             emphasis: { disabled: true },
             silent: true,
             z: 4,
             zlevel: 3
           },
           // Dynamic b-value line (red)
           {
             name: '动态b值',
             type: 'line',
             yAxisIndex: 1,
             data: alignedB,
             lineStyle: { color: '#FF0000', width: 2 },
             symbolSize: 4,
            symbol: 'circle',
             itemStyle: { color: '#FF0000', borderColor: '#FFFFFF', borderWidth: 1 },
             smooth: false,
             connectNulls: false,
             z: 4
           },
           // B-plus line (orange)
           {
             name: 'b-plus',
             type: 'line',
             yAxisIndex: 1,
             data: alignedBPlusB,
             lineStyle: { color: '#F26100', width: 2 },
            symbolSize: 4,
             symbol: 'circle',
             itemStyle: { color: '#F26100', borderColor: '#FFFFFF', borderWidth: 1 },
            smooth: false,
             connectNulls: false,
             z: 4
           },
           // b=1 baseline
           {
             name: 'b=1',
             type: 'line',
             yAxisIndex: 1,
             data: xAxisDates.map(() => 1),
             lineStyle: { color: '#000', width: 1.5 },
             symbol: 'none',
             silent: true,
            z: 2
           },
         ],
      };

      setChartOption(option);
    } finally {
      setIsCalculating(false);
    }
  }, [earthquakes]);

  // 统计信息
  const avgBValue = bSeries.length > 0 ? bSeries.reduce((s, v) => s + v.b, 0) / bSeries.length : null;
  const totalEarthquakes = earthquakes ? earthquakes.length : 0;
  
  let analysisSpanDays = 0;
  if (earthquakes && earthquakes.length > 0) {
    const dates = earthquakes
      .filter(eq => eq.occurred_at)
      .map(eq => new Date(eq.occurred_at))
      .sort((a, b) => a.getTime() - b.getTime());
    const first = dates[0];
    const last = dates[dates.length - 1];
    analysisSpanDays = Math.ceil((last.getTime() - first.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  }

  return (
    <div className="space-y-3">
      <div className="bg-slate-50 p-3 rounded">
        <h4 className="font-semibold mb-2 text-sm">动态<i>b</i>值时间序列分析</h4>
        <p className="text-xs text-slate-600 mb-2">
          基于累积历史数据分析地震活动的<i>b</i>值随时间变化
        </p>
        <div className="flex items-center gap-2 text-xs text-orange-600 bg-orange-50 p-1 rounded border-l-3 border-orange-400">
          <span className="text-orange-500">💡</span>
          <p className="flex-1">
            选择的日期越长，<i>b</i>值计算越稳定可靠
          </p>
        </div>
        
        {isCalculating && (
          <div className="text-xs text-blue-600 text-center py-1">
            正在分析数据...
          </div>
        )}
        
        {!isCalculating && (!earthquakes || earthquakes.length === 0) && (
          <div className="text-xs text-slate-500 text-center py-1">
            等待地震数据...
          </div>
        )}

      </div>

      {chartOption && (
        <div className="space-y-2">
          <div className="grid grid-cols-5 gap-2">
            <div className="bg-blue-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">地震总数</div>
              <div className="text-lg font-semibold text-blue-600">{totalEarthquakes}</div>
            </div>

            <div className="bg-green-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">时间跨度</div>
              <div className="text-lg font-semibold text-green-600">{analysisSpanDays}天</div>
            </div>

            <div className="bg-red-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">动态<i>b</i>值</div>
              <div className="text-lg font-semibold text-red-600">
                {avgBValue ? avgBValue.toFixed(3) : 'N/A'}
              </div>
            </div>

            <div className="bg-orange-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">b-plus事件</div>
              <div className="text-lg font-semibold text-orange-600">{bPlusSeries.length}</div>
            </div>

            <div className="bg-purple-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">风险事件</div>
              <div className="text-lg font-semibold text-purple-600">{riskEvents.length}</div>
            </div>
          </div>

          <div className="bg-white p-3 rounded border border-slate-200">
            <ReactECharts 
              option={chartOption} 
              style={{ height: '350px', width: '100%', overflow: 'visible' }}
              opts={{ renderer: 'canvas' }}
            />
          </div>
        </div>
      )}
    </div>
  );
} 