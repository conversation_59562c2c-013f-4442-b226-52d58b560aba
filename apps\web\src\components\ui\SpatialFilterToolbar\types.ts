export interface SearchResult {
  type: 'platform' | 'station';
  id: number;
  name: string;
  latitude: number;
  longitude: number;
}

export interface SpatialFilterState {
  center: [number, number] | null;
  radius: number | null;
}

export interface CoordinateInputProps {
  longitude: string;
  latitude: string;
  onLongitudeChange: (value: string) => void;
  onLatitudeChange: (value: string) => void;
  isPickingLocation: boolean;
  isDrawing: boolean;
  onStartLocationPicking: () => void;
  locationPickSuccess: boolean;
  successMessage: string;
}

export interface RadiusSliderProps {
  radius: string;
  onRadiusChange: (value: string) => void;
}

export interface SearchInputProps {
  searchQuery: string;
  searchResults: SearchResult[];
  isSearching: boolean;
  selectedSearchResult: SearchResult | null;
  onSearchQueryChange: (query: string) => void;
  onSelectSearchResult: (result: SearchResult) => void;
  onClearSelectedResult: () => void;
}

export interface ManualFilterPanelProps {
  isOpen: boolean;
  onClose: () => void;
  longitude: string;
  latitude: string;
  radius: string;
  searchQuery: string;
  searchResults: SearchResult[];
  isSearching: boolean;
  selectedSearchResult: SearchResult | null;
  isPickingLocation: boolean;
  isDrawing: boolean;
  locationPickSuccess: boolean;
  successMessage: string;
  onLongitudeChange: (value: string) => void;
  onLatitudeChange: (value: string) => void;
  onRadiusChange: (value: string) => void;
  onSearchQueryChange: (query: string) => void;
  onSelectSearchResult: (result: SearchResult) => void;
  onClearSelectedResult: () => void;
  onStartLocationPicking: () => void;
  onApplyFilter: () => void;
}

export interface ToolbarButtonsProps {
  isInitializing: boolean;
  isDrawing: boolean;
  isPickingLocation: boolean;
  hasActiveFilter: boolean;
  onStartDrawing: () => void;
  onToggleManualFilter: () => void;
  onZoomToFilterArea: () => void;
  onClearFilter: () => void;
  showManualFilter: boolean;
}
